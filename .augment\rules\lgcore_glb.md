---
type: "always_apply"
---

1. 用中文来回复；

2.  每次修改代码或增加功能时，都必须follow需求设计文档（PRD）和详细开发文档，而且开发代码时，必须结合全面系统审查已经开发好的功能和相关代码逻辑（可以用Serena MCP审查代码），符合真实生产应用场景来思考开发，遵循精准修改代码原则，并且确保前后端相关业务逻辑的代码保持同步和一致；

3. 每次在修改完代码或为新功能开发完代码后，必须仔细全面系统的检查验证更新过程中所有相关代码的正确性，绝对不能想当然地认为已经正确完成任务（没有调查没有发言权），这会给我带来错误的判断和后续的迭代，所以请务必客观真实的评价你所完成的任务的状态和问题；

4. 每次在修改代码或新增功能逻辑时，务必先完整系统的审查已开发好的最新版本的组件代码功能和业务逻辑，然后在完全理解新任务的要求后，思考给出精准有效的解决方案，最后进行精准精细的修改或开发新代码，切记不能随意盲目的修改已开发好的代码和业务逻辑，也不要去胡乱发挥修改不相关的代码，更不要产生冗余的重复功能的代码；

5. 针对比较难解决的问题，必须结合目前已经开发好的最新版本的相关组件代码，利用第一性原理和系统性思维进行全面本质的剖析，深入分析清楚问题的根源，再去精准的修复相关组件代码，等全部修改完后再全面检查验证修改点是不是真正解决了问题，不能遗漏掉或疏忽掉相关组件代码的修改，更不要引入其他新的代码问题或者业务逻辑错误问题或者功能缺失问题；

6. 每次修改功能或业务逻辑时，必须完整正确的理解上下文（包括截图，文档，链接，图片等资料）和提示词的内容要求（包括背景信息，约束条件，目标等方面），如果出现不明白或者不清楚或者模糊的或者没有考虑到的地方等情况，请停下来反馈给我并让我来确认；

7. 在增加新功能或业务逻辑时，不能随意去修改已经开发好的功能和业务逻辑，如果发现新功能开发会影响原来开发好的业务功能逻辑，或者对新功能的真实生产应用场景的要求不清楚，那么请反馈并让我来确认；


8. 当使用第三方工具服务SDK/API来开发时，请使用Context7 MCP或者Serena MCP去查询第三方工具对应版本的最新官方文档，并结合项目的技术架构、前后端技术栈，以及真实生产应用场景，按SDK/API相关开发文档来进行正确的集成开发