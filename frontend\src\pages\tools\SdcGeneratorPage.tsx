"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToolExecution, TaskStatus } from '../../hooks/useToolExecution';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Upload, Loader2, Download, FileText } from 'lucide-react';
import { motion } from 'framer-motion';

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACCEPTED_FILE_TYPES = ['.v', '.sv'];

const sdcFormSchema = z.object({
  projectName: z.string().min(3, "项目名称至少需要3个字符"),
  clockPeriod: z.coerce.number().positive("时钟周期必须是正数"),
  verilogFile: z.any()
    .refine((file): file is File => file instanceof File, "必须上传一个Verilog文件。")
    .refine((file) => file.size <= MAX_FILE_SIZE, `文件大小不能超过 5MB。`)
    .refine(
      (file) => ACCEPTED_FILE_TYPES.some(type => file.name.endsWith(type)),
      "只支持 .v 或 .sv 格式的文件。"
    ),
});

type SdcFormValues = z.infer<typeof sdcFormSchema>;

const SdcGeneratorPage: React.FC = () => {
    const { taskStatus, submitTask, resetTask, handleDownload } = useToolExecution();

    const form = useForm<SdcFormValues>({
        resolver: zodResolver(sdcFormSchema),
        defaultValues: {
            projectName: 'my_design',
            clockPeriod: 10.0,
            verilogFile: undefined,
        },
    });

    const onSubmit = (data: SdcFormValues) => {
        const { projectName, clockPeriod, verilogFile } = data;
        submitTask({
            toolId: 'sdc-generator',
            parameters: { projectName, clockPeriod },
            inputFile: verilogFile,
        });
    };
    
    return (
        <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto max-w-2xl p-4 sm:p-6 lg:p-8"
        >
            {taskStatus.status !== 'IDLE' ? (
                <TaskResultDisplay taskStatus={taskStatus} resetTask={resetTask} handleDownload={handleDownload} />
            ) : (
                <Card className="shadow-lg">
                    <CardHeader>
                        <CardTitle className="text-2xl md:text-3xl font-bold">SDC 约束文件生成器</CardTitle>
                        <CardDescription>上传您的Verilog设计文件，自动生成基础的时序约束(SDC)文件。</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <FormField control={form.control} name="projectName" render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>项目名称</FormLabel>
                                        <FormControl><Input placeholder="例如: my_awesome_chip" {...field} /></FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )} />
                                <FormField control={form.control} name="clockPeriod" render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>主时钟周期 (ns)</FormLabel>
                                        <FormControl><Input type="number" step="0.1" {...field} /></FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )} />
                                
                                <FormField control={form.control} name="verilogFile" render={({ field: { onChange, value, ...rest } }) => (
                                    <FormItem>
                                        <FormLabel>上传 Verilog 文件</FormLabel>
                                        <FormControl>
                                            <Label htmlFor="verilog-upload" className={`flex items-center space-x-2 border-2 border-dashed rounded-lg p-4 cursor-pointer transition-colors ${value ? 'border-green-500 bg-green-50' : 'border-gray-300 hover:border-blue-500'}`}>
                                                {value ? <FileText className="h-5 w-5 text-green-700" /> : <Upload className="h-5 w-5 text-gray-500" />}
                                                <span className={value ? 'text-green-800' : 'text-gray-600'}>{value?.name || "点击或拖拽文件上传 (.v, .sv)"}</span>
                                            </Label>
                                            <Input id="verilog-upload" type="file" className="hidden" accept={ACCEPTED_FILE_TYPES.join(',')} onChange={e => onChange(e.target.files?.[0])} {...rest} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )} />

                                <Button type="submit" className="w-full" disabled={form.formState.isSubmitting}>
                                    {form.formState.isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                                    {form.formState.isSubmitting ? '正在提交...' : '生成SDC文件'}
                                </Button>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            )}
        </motion.div>
    );
};

const TaskResultDisplay = ({ taskStatus, resetTask, handleDownload }: { taskStatus: TaskStatus, resetTask: () => void, handleDownload: (type: 'result' | 'log') => void }) => {
    return (
         <div className="container mx-auto max-w-2xl p-4 sm:p-6 lg:p-8">
            <Card>
                <CardHeader>
                    <CardTitle>任务状态 (ID: {taskStatus.taskId})</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {taskStatus.status === 'POLLING' && (
                        <div className="text-center">
                            <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto" />
                            <p className="mt-2 text-lg">正在执行任务... {taskStatus.progress}%</p>
                        </div>
                    )}
                     {taskStatus.errorMessage && (
                        <Alert variant="destructive">
                            <AlertTitle>任务失败</AlertTitle>
                            <AlertDescription>{taskStatus.errorMessage}</AlertDescription>
                        </Alert>
                    )}
                    {taskStatus.status === 'COMPLETED' && (
                        <Alert variant="default" className="border-green-500 text-green-700">
                             <AlertTitle>任务成功完成！</AlertTitle>
                            <AlertDescription>您可以下载结果文件和日志。</AlertDescription>
                        </Alert>
                    )}
                     <div className="flex justify-center gap-4 pt-4">
                        {taskStatus.status === 'COMPLETED' && taskStatus.resultUrl && (
                             <Button onClick={() => handleDownload('result')}><Download className="mr-2 h-4 w-4" />下载结果</Button>
                        )}
                        {taskStatus.logUrl && (
                             <Button variant="secondary" onClick={() => handleDownload('log')}><FileText className="mr-2 h-4 w-4" />下载日志</Button>
                        )}
                    </div>
                    <Button onClick={resetTask} className="w-full">开始新任务</Button>
                </CardContent>
            </Card>
        </div>
    );
}

export default SdcGeneratorPage; 