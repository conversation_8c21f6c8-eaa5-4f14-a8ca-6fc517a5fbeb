import nodemailer from 'nodemailer';

// 创建一个可复用的 transporter 对象
// 在开发环境中，我们使用 Ethereal 来捕获邮件
let transporter: nodemailer.Transporter;

async function getTestAccount() {
  let testAccount = await nodemailer.createTestAccount();
  console.log('Ethereal test account created:');
  console.log(`User: ${testAccount.user}`);
  console.log(`Pass: ${testAccount.pass}`);
  console.log(`Preview URL: (use any message URL from console logs)`);
  return testAccount;
}

// 导出一个异步函数来获取 transporter
export const getTransporter = async () => {
  if (!transporter) {
    const testAccount = await getTestAccount();
    transporter = nodemailer.createTransport({
      host: 'smtp.ethereal.email',
      port: 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: testAccount.user,
        pass: testAccount.pass,
      },
    });
  }
  return transporter;
};

export const sendVerificationEmail = async (email: string, token: string) => {
  const mailer = await getTransporter();
  const backendUrl = process.env.API_BASE_URL || 'http://localhost:8080';
  const verificationUrl = `${backendUrl}/api/v1/auth/verify-email?token=${token}`;

  const info = await mailer.sendMail({
    from: '"ChipCore" <<EMAIL>>',
    to: email,
    subject: '欢迎来到 ChipCore - 请验证您的邮箱',
    text: `请点击此链接验证您的邮箱: ${verificationUrl}`,
    html: `<b>请点击此链接验证您的邮箱:</b> <a href="${verificationUrl}">${verificationUrl}</a>`,
  });

  console.log('Message sent: %s', info.messageId);
  // 在 Ethereal 中预览邮件的链接
  console.log('Preview URL: %s', nodemailer.getTestMessageUrl(info));
};

export const sendPasswordResetEmail = async (email: string, token: string) => {
  const mailer = await getTransporter();
  const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
  const resetUrl = `${frontendUrl}/auth/reset-password?token=${token}`;

  const info = await mailer.sendMail({
    from: '"ChipCore" <<EMAIL>>',
    to: email,
    subject: 'ChipCore 密码重置请求',
    text: `您正在重置密码。请点击此链接设置新密码: ${resetUrl}`,
    html: `<b>您正在重置密码。</b><br/>请点击此链接设置新密码: <a href="${resetUrl}">${resetUrl}</a><br/>如果您没有请求此操作，请忽略此邮件。`,
  });

  console.log('Password reset message sent: %s', info.messageId);
  console.log('Preview URL: %s', nodemailer.getTestMessageUrl(info));
};