# Application
PORT=8080
FRONTEND_URL=http://localhost:3000

# This should be the same secret used to sign JWTs
COOKIE_SECRET=YOUR_VERY_SECRET_COOKIE_KEY_12345678901234567890

# Database & Redis
DATABASE_URL=postgresql://postgres:password@localhost:5432/chipcore_dev
REDIS_URL=redis://localhost:6379/0

# Security
JWT_SECRET=YOUR_SUPER_SECRET_JWT_KEY_12345678901234567890
JWT_EXPIRES_IN=1d

NODE_ENV=development

# Alipay Configuration (测试环境)
ALIPAY_APP_ID=9021000122671080
ALIPAY_APP_PRIVATE_KEY_PATH=alipay_dev_private.pem
ALIPAY_PUBLIC_KEY_PATH=alipay_dev_public.pem
ALIPAY_NOTIFY_URL=http://localhost:8080/api/v1/payment/notify/alipay

# WeChat Pay Configuration (测试环境)
WECHAT_APP_ID=wxd678efh567hg6787
WECHAT_MCH_ID=1230000109
WECHAT_CERTIFICATE_SERIAL_NO=5157F09EFDC096DE15EBE81A47057A7232F1B8E1
WECHAT_API_V3_KEY=dev_wechat_api_v3_key_32_chars_long_abcdef123456
WECHAT_NOTIFY_URL=http://localhost:8080/api/v1/payment/notify/wechat
API_BASE_URL=http://localhost:8080

# Aliyun Credentials (for Backend and Worker initial access)
ALIYUN_ACCESS_KEY_ID=YOUR_RAM_USER_ACCESS_KEY_ID
ALIYUN_ACCESS_KEY_SECRET=YOUR_RAM_USER_ACCESS_KEY_SECRET

# Aliyun RAM Role to be assumed by containers
# This role should have minimal OSS read/write permissions
ALIYUN_RAM_ROLE_ARN=arn:aws:iam::123456789012:role/YourToolExecutionRoleForContainer
ALIYUN_STS_REGION=cn-hangzhou

# Aliyun OSS
OSS_REGION=cn-hangzhou
OSS_BUCKET_USER_INPUT=your-app-user-input
OSS_BUCKET_JOB_RESULTS=your-app-job-results
OSS_BUCKET_JOB_LOGS=your-app-job-logs

# Python Worker Configuration
ECS_TOTAL_CPU=8
ECS_TOTAL_MEMORY_GB=64
JOB_CPU_REQUEST=2
JOB_MEMORY_REQUEST_GB=16
WORKER_ID=worker-main-01
# This can be dynamically fetched in a multi-node setup
ECS_INSTANCE_ID=ecs-single-node-dev
TASK_QUEUE_NAME=task_queue
