/**
 * 支付流程测试脚本
 * 使用方法: node test-payment.js [orderId] [paymentMethod]
 * 示例: node test-payment.js cm123456 ALIPAY
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:8080';

// 模拟支付宝支付成功回调
async function simulateAlipayCallback(orderId, amount = '0.01') {
  console.log('🔄 模拟支付宝支付成功回调...');
  
  const params = new URLSearchParams({
    app_id: '9021000122671080',
    auth_app_id: '9021000122671080',
    buyer_id: '2088102177846875',
    buyer_logon_id: 'csq***@sandbox.com',
    buyer_pay_amount: amount,
    charset: 'utf-8',
    gmt_create: new Date().toISOString().replace('T', ' ').substring(0, 19),
    gmt_payment: new Date().toISOString().replace('T', ' ').substring(0, 19),
    notify_id: `notify_${Date.now()}`,
    notify_time: new Date().toISOString().replace('T', ' ').substring(0, 19),
    notify_type: 'trade_status_sync',
    out_trade_no: orderId,
    receipt_amount: amount,
    seller_email: '<EMAIL>',
    seller_id: '2088102177649450',
    subject: `Membership Subscription - ${orderId}`,
    total_amount: amount,
    trade_no: `2025070822001446870${Math.floor(Math.random() * 1000000)}`,
    trade_status: 'TRADE_SUCCESS',
    version: '1.0',
    sign_type: 'RSA2',
    sign: 'mock_signature_for_testing'
  });

  try {
    const response = await axios.post(
      `${BASE_URL}/api/v1/payment/notify/alipay`,
      params.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );
    console.log('✅ 支付宝回调成功:', response.status, response.data);
    return true;
  } catch (error) {
    console.error('❌ 支付宝回调失败:', error.response?.data || error.message);
    return false;
  }
}

// 模拟微信支付成功回调
async function simulateWechatCallback(orderId, amount = '0.01') {
  console.log('🔄 模拟微信支付成功回调...');
  
  const notifyData = {
    id: `notify_${Date.now()}`,
    create_time: new Date().toISOString(),
    event_type: 'TRANSACTION.SUCCESS',
    resource_type: 'encrypt-resource',
    resource: {
      original_type: 'transaction',
      algorithm: 'AEAD_AES_256_GCM',
      ciphertext: JSON.stringify({
        out_trade_no: orderId,
        transaction_id: `wx${Date.now()}${Math.floor(Math.random() * 1000)}`,
        trade_type: 'NATIVE',
        trade_state: 'SUCCESS',
        trade_state_desc: '支付成功',
        bank_type: 'OTHERS',
        success_time: new Date().toISOString(),
        payer: {
          openid: 'mock_openid_123456'
        },
        amount: {
          total: Math.round(parseFloat(amount) * 100), // 微信支付使用分
          payer_total: Math.round(parseFloat(amount) * 100),
          currency: 'CNY',
          payer_currency: 'CNY'
        }
      }),
      associated_data: 'transaction',
      nonce: 'mock_nonce'
    },
    summary: '支付成功'
  };

  try {
    const response = await axios.post(
      `${BASE_URL}/api/v1/payment/notify/wechat`,
      notifyData,
      {
        headers: {
          'Content-Type': 'application/json',
          'Wechatpay-Signature': 'mock_signature',
          'Wechatpay-Timestamp': Math.floor(Date.now() / 1000).toString(),
          'Wechatpay-Nonce': 'mock_nonce',
          'Wechatpay-Serial': '5157F09EFDC096DE15EBE81A47057A7232F1B8E1',
        },
      }
    );
    console.log('✅ 微信支付回调成功:', response.status, response.data);
    return true;
  } catch (error) {
    console.error('❌ 微信支付回调失败:', error.response?.data || error.message);
    return false;
  }
}

// 检查订单状态
async function checkOrderStatus(orderId) {
  try {
    const response = await axios.get(`${BASE_URL}/api/v1/orders/${orderId}`);
    console.log('📊 订单状态:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ 检查订单状态失败:', error.response?.data || error.message);
    return null;
  }
}

// 主测试函数
async function runPaymentTest() {
  const orderId = process.argv[2];
  const paymentMethod = process.argv[3] || 'ALIPAY';
  const amount = process.argv[4] || '0.01';

  if (!orderId) {
    console.error('❌ 请提供订单ID');
    console.log('使用方法: node test-payment.js [orderId] [paymentMethod] [amount]');
    console.log('示例: node test-payment.js cm123456 ALIPAY 0.01');
    process.exit(1);
  }

  console.log('🚀 开始支付流程测试...');
  console.log(`📋 订单ID: ${orderId}`);
  console.log(`💳 支付方式: ${paymentMethod}`);
  console.log(`💰 金额: ${amount}元`);
  console.log('─'.repeat(50));

  try {
    // 1. 检查初始订单状态
    console.log('\n1️⃣ 检查初始订单状态...');
    const initialOrder = await checkOrderStatus(orderId);
    if (!initialOrder) {
      throw new Error('订单不存在');
    }
    
    if (initialOrder.status !== 'PENDING') {
      console.log(`⚠️ 订单状态不是PENDING: ${initialOrder.status}`);
    }

    // 2. 模拟支付成功
    console.log('\n2️⃣ 模拟支付成功...');
    let callbackSuccess = false;
    
    if (paymentMethod.toUpperCase() === 'ALIPAY') {
      callbackSuccess = await simulateAlipayCallback(orderId, amount);
    } else if (paymentMethod.toUpperCase() === 'WECHAT_PAY') {
      callbackSuccess = await simulateWechatCallback(orderId, amount);
    } else {
      throw new Error(`不支持的支付方式: ${paymentMethod}`);
    }

    if (!callbackSuccess) {
      throw new Error('支付回调模拟失败');
    }

    // 3. 等待处理
    console.log('\n3️⃣ 等待回调处理...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 4. 检查订单状态更新
    console.log('\n4️⃣ 检查订单状态更新...');
    const updatedOrder = await checkOrderStatus(orderId);
    
    if (updatedOrder && updatedOrder.status === 'PAID') {
      console.log('✅ 订单状态更新成功: PENDING → PAID');
    } else {
      console.log('❌ 订单状态更新失败:', updatedOrder?.status || '未知');
    }

    console.log('\n🎉 支付流程测试完成!');
    console.log('─'.repeat(50));
    console.log('📊 测试结果:');
    console.log(`   初始状态: ${initialOrder.status}`);
    console.log(`   最终状态: ${updatedOrder?.status || '未知'}`);
    console.log(`   回调成功: ${callbackSuccess ? '是' : '否'}`);

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
runPaymentTest();
