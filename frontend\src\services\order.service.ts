// This is a mock service for creating orders.
// In a real application, this would make a request to the backend API.
// For now, it simulates a successful API call and returns mock data.

interface CreateOrderPayload {
  planId: string;
  billingCycle: string;
  paymentMethod: 'ALIPAY' | 'WECHAT_PAY';
}

// Simulate a network delay
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const createOrder = async (payload: CreateOrderPayload) => {
  await sleep(1500); // Simulate network latency

  // 创建订单请求

  // Mock a successful response
  // In a real scenario, the qrCode and orderNo would come from the backend.
  const mockResponse = {
    data: {
      order: {
        orderNo: `MOCK_${Date.now()}`,
        planId: payload.planId,
        status: 'PENDING',
      },
      paymentDetails: {
        // This is a generic QR code value for demonstration.
        // It's a URL pointing to the project's GitHub, not a real payment QR code.
        qrCode: 'https://github.com/onlineictech/LogicCore',
      },
    },
  };

  // To simulate an error, you could uncomment the following lines:
  // throw new Error("This is a simulated error from the server.");

  return mockResponse;
};

export const getMyOrders = async () => {
  await sleep(1000);

  // Mock a list of orders
  const mockResponse = {
    data: [
      {
        orderNo: 'MOCK_1672531200000',
        planName: '专业版 - 年度',
        amount: 1299.00,
        status: 'PAID',
        createdAt: new Date('2023-01-01T12:00:00Z').toISOString(),
      },
      {
        orderNo: 'MOCK_1669852800000',
        planName: '专业版 - 月度',
        amount: 129.00,
        status: 'PAID',
        createdAt: new Date('2022-12-01T10:00:00Z').toISOString(),
      },
      {
        orderNo: 'MOCK_1667260800000',
        planName: '免费版',
        amount: 0.00,
        status: 'PAID',
        createdAt: new Date('2022-11-01T08:00:00Z').toISOString(),
      },
    ],
  };

  return mockResponse;
};

export const getOrderDetails = async (orderNo: string) => {
  await sleep(1000);

  // 获取订单详情

  // Mock a detailed order object. In a real app, this would come from the backend.
  // We can return a static object since we're just mocking.
  const mockOrder = {
    orderNo,
    status: 'PAID',
    createdAt: new Date('2023-01-01T12:00:00Z').toISOString(),
    updatedAt: new Date('2023-01-01T12:05:00Z').toISOString(),
    amount: 1299.00,
    plan: {
      name: '专业版 - 年度',
      description: '解锁所有高级功能，享受全年无限制访问。',
    },
    payment: {
      method: 'WECHAT_PAY',
      transactionId: 'MOCK_TRANSACTION_123456789',
      paidAt: new Date('2023-01-01T12:05:00Z').toISOString(),
    },
    user: {
      email: '<EMAIL>',
      name: '测试用户',
    },
    billingAddress: {
      name: '测试用户',
      address: '中国北京市海淀区中关村大街1号',
      phone: '13800138000',
    },
  };

  return { data: mockOrder };
}; 