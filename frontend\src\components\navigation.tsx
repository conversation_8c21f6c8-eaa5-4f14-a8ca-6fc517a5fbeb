import { useState, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { 
  ChevronDown, 
  Menu, 
  X,
  User,
  LogOut,
  LayoutDashboard
} from "lucide-react";
import useOnClickOutside from "@/hooks/use-on-click-outside";
import { useAuth } from "@/contexts/auth.context";

const tools = [
  { name: "SDC高效生成", icon: "fas fa-microchip", href: "/tools/sdc-generator" },
  { name: "CLK电路自动生成", icon: "fas fa-project-diagram", href: "/tools/clk-generator" },
  { name: "Memory数据生成", icon: "fas fa-memory", href: "/tools/memory-generator" }
];

export default function Navigation() {
  const { isAuthenticated, user, logout } = useAuth();
  const navigate = useNavigate();

  const [isToolsOpen, setIsToolsOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

  const toolsMenuRef = useRef<HTMLDivElement>(null);
  const userMenuRef = useRef<HTMLDivElement>(null);

  useOnClickOutside(toolsMenuRef, () => setIsToolsOpen(false));
  useOnClickOutside(userMenuRef, () => setIsUserMenuOpen(false));

  const handleLogout = () => {
    logout();
    setIsUserMenuOpen(false);
    setIsMobileMenuOpen(false);
    navigate('/');
  };

  const UserMenu = () => (
    <div className="relative" ref={userMenuRef}>
      <Button variant="ghost" size="sm" onClick={() => setIsUserMenuOpen(!isUserMenuOpen)} className="flex items-center">
        <User className="h-5 w-5 mr-2" />
        <span>{user?.name || user?.email}</span>
        <ChevronDown className={`ml-1 h-4 w-4 transition-transform ${isUserMenuOpen ? 'rotate-180' : ''}`} />
      </Button>
      {isUserMenuOpen && (
        <div className="absolute top-full right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-100 z-50">
          <div className="py-2">
            <div className="px-4 py-3 border-b">
              <p className="text-sm font-semibold">您好, {user?.name || '用户'}</p>
              <p className="text-xs text-gray-500 truncate">{user?.email}</p>
            </div>
            <Link to="/profile" onClick={() => setIsUserMenuOpen(false)} className="flex items-center w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors">
              <LayoutDashboard className="mr-3 h-4 w-4" /> 个人中心
            </Link>
            <button onClick={handleLogout} className="flex items-center w-full text-left px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors">
              <LogOut className="mr-3 h-4 w-4" /> 退出登录
            </button>
          </div>
        </div>
      )}
    </div>
  );

  const AuthButtons = () => (
    <div className="hidden md:flex items-center space-x-2">
      <Button variant="ghost" size="sm" asChild>
        <Link to="/auth/login">登录</Link>
      </Button>
      <Button size="sm" className="gradient-bg-orange text-white hover:opacity-90" asChild>
        <Link to="/auth/register">注册</Link>
      </Button>
    </div>
  );

  return (
    <nav className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200 sticky top-0 z-40">
      <div className="max-w-8xl mx-auto px-6 sm:px-8 lg:px-12">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <div className="flex-shrink-0">
              <h1 className="text-2xl font-bold gradient-text-orange">ChipCore</h1>
            </div>
          </Link>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
              首页
            </Link>
            
            {/* Tools Dropdown */}
            <div className="relative" ref={toolsMenuRef}>
              <button 
                className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                onClick={() => setIsToolsOpen(!isToolsOpen)}
              >
                工具
                <ChevronDown className={`ml-1 h-4 w-4 transition-transform ${isToolsOpen ? 'rotate-180' : ''}`} />
              </button>
              
              {isToolsOpen && (
                <div className="absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-100">
                  <div className="py-2">
                    {tools.map((tool, index) => (
                      <Link
                        key={index}
                        to={tool.href}
                        onClick={() => setIsToolsOpen(false)}
                        className="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 transition-colors"
                      >
                        <i className={`${tool.icon} mr-3 text-blue-500`}></i>
                        {tool.name}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <Link to="/membership" className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
              会员
            </Link>
            <Link to="/contact" className="text-gray-600 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
              联系
            </Link>
          </div>
          
          {/* Auth Buttons */}
          <div className="flex items-center">
            <div className="hidden md:flex items-center space-x-4">
              {isAuthenticated ? <UserMenu /> : <AuthButtons />}
            </div>
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>
        
        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
              <Link to="/" onClick={() => setIsMobileMenuOpen(false)} className="block px-3 py-2 text-gray-600 hover:text-blue-600">首页</Link>
              <div className="px-3 py-2">
                <p className="text-gray-500 font-medium mb-2">工具</p>
                {tools.map((tool, index) => (
                  <Link
                    key={index}
                    to={tool.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="block px-4 py-2 text-sm text-gray-600 hover:text-blue-600"
                  >
                    <i className={`${tool.icon} mr-2 text-blue-500`}></i>
                    {tool.name}
                  </Link>
                ))}
              </div>
              <Link to="/membership" onClick={() => setIsMobileMenuOpen(false)} className="block px-3 py-2 text-gray-600 hover:text-blue-600">会员</Link>
              <Link to="/contact" onClick={() => setIsMobileMenuOpen(false)} className="block px-3 py-2 text-gray-600 hover:text-blue-600">联系</Link>
              <div className="px-3 py-2 space-y-2 border-t mt-2 pt-2">
                {isAuthenticated ? (
                  <>
                    <Link to="/profile" onClick={() => setIsMobileMenuOpen(false)} className="flex items-center w-full text-left px-3 py-2 text-gray-600 hover:text-blue-600">
                      <LayoutDashboard className="mr-3 h-4 w-4" /> 个人中心
                    </Link>
                    <Button variant="ghost" size="sm" className="w-full justify-start text-red-600" onClick={handleLogout}>
                      <LogOut className="mr-3 h-4 w-4" /> 退出登录
                    </Button>
                  </>
                ) : (
                  <>
                    <Button variant="ghost" size="sm" className="w-full justify-start" asChild>
                       <Link to="/auth/login" onClick={() => setIsMobileMenuOpen(false)}>登录</Link>
                    </Button>
                    <Button size="sm" className="w-full gradient-bg-orange text-white" asChild>
                      <Link to="/auth/register" onClick={() => setIsMobileMenuOpen(false)}>注册</Link>
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
