import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/auth.context';

const ProtectedRoute: React.FC = () => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    // 在认证状态加载时，可以显示一个全局的加载指示器
    // 为简单起见，我们暂时返回null，以避免页面闪烁
    return null; 
  }

  if (!isAuthenticated) {
    // 将用户重定向到登录页，并保存他们试图访问的位置
    //以便登录后可以将他们重定向回来
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <Outlet />; // 如果已认证，渲染子路由
};

export default ProtectedRoute; 