import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth.context';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate, Link } from 'react-router-dom';
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Loader2, User, CreditCard, History, LogOut, ExternalLink, Pencil } from "lucide-react";
import { getMySubscription, cancelSubscription } from '@/services/subscription.service';
import { getMyOrders } from '@/services/order.service';
import { motion, AnimatePresence } from "framer-motion";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

// Mock Interfaces
interface Subscription {
  plan: { name: string; };
  status: string;
  endDate: string;
  autoRenew: boolean;
}
interface Order {
  orderNo: string;
  planName: string;
  amount: number;
  status: string;
  createdAt: string;
}

const MotionDiv = motion.div;

const ProfilePage: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (!user) {
        navigate('/login');
        return;
      }
      
      setLoading(true);
      try {
        const [subRes, ordersRes] = await Promise.all([
          getMySubscription().catch(() => null),
          getMyOrders().catch(() => null),
        ]);

        if (subRes) setSubscription(subRes.data);
        if (ordersRes) setOrders(ordersRes.data);

      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, navigate]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };
  
  const handleCancelSubscription = async () => {
      if (!window.confirm("您确定要取消自动续订吗？您当前的会员权益将保留至到期日。")) return;
      try {
        await cancelSubscription();
        const subRes = await getMySubscription();
        setSubscription(subRes.data);
      } catch(err) {
        console.error("Error cancelling subscription:", err);
      }
  }

  const tabContentVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 },
  };

  if (loading) {
    return (
      <div className="container mx-auto py-20 flex justify-center items-center">
        <Loader2 className="h-16 w-16 animate-spin text-primary" />
      </div>
    );
  }

  if (!user) return null;

  return (
    <>
      <div className="min-h-screen bg-gray-50/50">
        <motion.div 
          className="container mx-auto py-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="max-w-4xl mx-auto shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-3xl font-bold">个人中心</CardTitle>
                <CardDescription>在这里管理您的账户信息、会员订阅和支付历史。</CardDescription>
              </div>
              <Button onClick={handleLogout} variant="ghost" size="sm">
                <LogOut className="mr-2 h-4 w-4" />
                退出登录
              </Button>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="profile" className="w-full">
                <TabsList className="grid w-full grid-cols-3 bg-gray-100/80">
                  <TabsTrigger value="profile"><User className="mr-2 h-4 w-4" />基本资料</TabsTrigger>
                  <TabsTrigger value="subscription"><CreditCard className="mr-2 h-4 w-4" />会员订阅</TabsTrigger>
                  <TabsTrigger value="history"><History className="mr-2 h-4 w-4" />支付历史</TabsTrigger>
                </TabsList>
                <AnimatePresence mode="wait">
                  <TabsContent value="profile" className="mt-6">
                    <MotionDiv key="profile" variants={tabContentVariants} initial="hidden" animate="visible" exit="exit" transition={{ duration: 0.3 }}>
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                            <div>
                                <CardTitle>账户信息</CardTitle>
                                <CardDescription>您的基础账户详情。</CardDescription>
                            </div>
                            <Button variant="outline" size="sm" onClick={() => setIsEditDialogOpen(true)}>
                                <Pencil className="mr-2 h-4 w-4"/>
                                编辑
                            </Button>
                        </CardHeader>
                        <CardContent className="space-y-4 pt-4">
                            <InfoRow label="邮箱地址" value={user.email} />
                            <InfoRow label="用户昵称" value={user.name || '未设置'} />
                            <InfoRow label="邮箱状态" value={user.isVerified ? '已验证' : '未验证'} badgeVariant={user.isVerified ? 'success' : 'destructive'} />
                            <InfoRow label="加入时间" value={new Date(user.createdAt).toLocaleDateString()} />
                        </CardContent>
                      </Card>
                    </MotionDiv>
                  </TabsContent>
                  <TabsContent value="subscription" className="mt-6">
                     <MotionDiv key="subscription" variants={tabContentVariants} initial="hidden" animate="visible" exit="exit" transition={{ duration: 0.3 }}>
                       {subscription ? (
                         <Card>
                            <CardHeader>
                               <CardTitle>我的订阅</CardTitle>
                               <CardDescription>您当前的会员计划详情。</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4 pt-4">
                                <div className="flex justify-between items-center p-4 border rounded-lg bg-blue-50/50">
                                    <div>
                                        <h4 className="font-bold text-lg">当前方案: <Badge className="bg-primary hover:bg-primary/90">{subscription.plan.name}</Badge></h4>
                                        <p className="text-sm text-muted-foreground">状态: {subscription.status === 'ACTIVE' ? '有效' : '已取消'}</p>
                                    </div>
                                    <div className="text-right">
                                        <p>将于 {new Date(subscription.endDate).toLocaleDateString()} 到期</p>
                                        <p className="text-sm text-muted-foreground">{subscription.autoRenew ? "将自动续订" : "订阅已取消"}</p>
                                    </div>
                                </div>
                                {subscription.autoRenew && (
                                    <Button onClick={handleCancelSubscription} variant="outline">取消自动续订</Button>
                                )}
                                {!subscription.autoRenew && subscription.status === 'ACTIVE' && (
                                     <Button onClick={() => navigate('/membership')} className="gradient-bg-orange">续订会员</Button>
                                )}
                            </CardContent>
                         </Card>
                       ) : (
                         <div className="text-center py-12 border-2 border-dashed rounded-lg">
                           <p className="text-muted-foreground">您当前没有有效的会员订阅。</p>
                           <Button onClick={() => navigate('/membership')} className="mt-4 gradient-bg-orange">查看会员方案</Button>
                         </div>
                       )}
                     </MotionDiv>
                  </TabsContent>
                  <TabsContent value="history" className="mt-6">
                    <MotionDiv key="history" variants={tabContentVariants} initial="hidden" animate="visible" exit="exit" transition={{ duration: 0.3 }}>
                        <Card>
                            <CardHeader>
                               <CardTitle>支付历史</CardTitle>
                               <CardDescription>您最近的交易记录。</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>订单号</TableHead>
                                            <TableHead>订阅方案</TableHead>
                                            <TableHead>金额</TableHead>
                                            <TableHead>状态</TableHead>
                                            <TableHead>日期</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                    {orders.length > 0 ? (
                                        orders.slice(0, 5).map(order => (
                                        <TableRow key={order.orderNo} onClick={() => navigate(`/order/details/${order.orderNo}`)} className="cursor-pointer hover:bg-gray-50">
                                            <TableCell className="font-mono">{order.orderNo}</TableCell>
                                            <TableCell>{order.planName}</TableCell>
                                            <TableCell>¥{order.amount.toFixed(2)}</TableCell>
                                            <TableCell><Badge variant={order.status === 'PAID' ? 'success' : 'secondary'}>{order.status}</Badge></TableCell>
                                            <TableCell>{new Date(order.createdAt).toLocaleDateString()}</TableCell>
                                        </TableRow>
                                        ))
                                    ) : (
                                        <TableRow><TableCell colSpan={5} className="text-center h-24">没有找到支付记录。</TableCell></TableRow>
                                    )}
                                    </TableBody>
                                </Table>
                                {orders.length > 5 && (
                                    <div className="text-center mt-4">
                                        <Button variant="link" asChild>
                                            <Link to="/user/orders">查看全部历史记录 <ExternalLink className="ml-2 h-4 w-4" /></Link>
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </MotionDiv>
                  </TabsContent>
                </AnimatePresence>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>
      </div>
      <EditProfileDialog isOpen={isEditDialogOpen} onOpenChange={setIsEditDialogOpen} nickname={user.name || ''} />
    </>
  );
};

const InfoRow = ({ label, value, badgeVariant }: { label: string; value: string; badgeVariant?: "success" | "destructive" | "default" }) => (
    <div className="flex items-center justify-between py-2 border-b">
        <p className="text-sm font-medium text-muted-foreground">{label}</p>
        {badgeVariant ? <Badge variant={badgeVariant}>{value}</Badge> : <p className="text-sm">{value}</p>}
    </div>
);

const EditProfileDialog = ({ isOpen, onOpenChange, nickname }: { isOpen: boolean; onOpenChange: (open: boolean) => void; nickname: string }) => {
    // Mock state for editing
    const [currentNickname, setCurrentNickname] = useState(nickname);

    const handleSave = () => {
        console.log("Saving new nickname:", currentNickname);
        // Here you would call an API service to update the user's profile
        onOpenChange(false);
    }
    
    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>编辑个人资料</DialogTitle>
                    <DialogDescription>
                        在这里更新您的账户信息。点击保存以应用更改。
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="nickname" className="text-right">昵称</Label>
                        <Input id="nickname" value={currentNickname} onChange={(e) => setCurrentNickname(e.target.value)} className="col-span-3" />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>取消</Button>
                    <Button type="submit" onClick={handleSave}>保存更改</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export default ProfilePage; 